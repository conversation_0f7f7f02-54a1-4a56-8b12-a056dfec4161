import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'core/config/firebase_config.dart';
import 'core/storage/storage_service.dart';
import 'core/storage/web_storage_service.dart';
import 'core/providers/app_providers.dart';
import 'core/theme/theme_provider.dart';
import 'core/localization/language_provider.dart';
import 'core/debug/cors_test.dart';
import 'core/debug/web_storage_test.dart';
import 'core/debug/android_notification_test.dart';
import 'core/services/firebase_messaging_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/realtime_service_initializer.dart';
import 'features/auth/providers/auth_provider.dart';
import 'generated/l10n/app_localizations.dart';
import 'dart:developer' as developer;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Configure channel buffers for web platform to handle lifecycle warnings
  if (kIsWeb) {
    // Increase buffer size for lifecycle channel to prevent message discarding
    WidgetsBinding.instance.defaultBinaryMessenger.setMessageHandler(
      'flutter/lifecycle',
      null,
    );
  }

  // Initialize Firebase (guard against duplicate initialization on hot restart)
  if (Firebase.apps.isEmpty) {
    if (kIsWeb) {
      await Firebase.initializeApp(options: FirebaseConfig.currentPlatform);
    } else {
      await Firebase.initializeApp();
    }
  }

  // Set up background message handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // Initialize FCM immediately (like Customer app pattern)
  await FirebaseMessagingService.initialize();

  // Initialize Notifications
  await NotificationService.initialize();

  // Initialize storage service (use WebStorageService for better web support)
  await WebStorageService.init();
  await StorageService.init(); // Keep for backward compatibility

  // Run tests on web platform
  if (kIsWeb) {
    // Run tests after a short delay to avoid blocking app startup
    Future.delayed(const Duration(seconds: 1), () {
      WebStorageTest.runAllTests();
    });
    Future.delayed(const Duration(seconds: 3), () {
      CorsTest.runAllTests();
    });
  }

  // Run Android notification tests on Android platform (debug mode only)
  if (!kIsWeb && kDebugMode) {
    Future.delayed(const Duration(seconds: 2), () {
      AndroidNotificationTest.runAllTests();
    });
  }

  runApp(const ProviderScope(child: DaltiProviderApp()));
}

class DaltiProviderApp extends ConsumerWidget {
  const DaltiProviderApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeState = ref.watch(themeNotifierProvider);
    final languageState = ref.watch(languageProvider);
    final router = ref.watch(routerProvider);

    // Initialize real-time services when user is authenticated
    ref.listen(authNotifierProvider, (previous, next) {
      if (next.isAuthenticated && !RealtimeServiceInitializer.isInitialized) {
        developer.log(
          '[DaltiProviderApp] User authenticated - initializing real-time services...',
        );
        ref.initializeRealtimeServicesWithRetry().then((success) {
          if (success) {
            developer.log(
              '[DaltiProviderApp] ✅ Real-time services initialized successfully',
            );
          } else {
            developer.log(
              '[DaltiProviderApp] ❌ Failed to initialize real-time services',
            );
          }
        });
      } else if (!next.isAuthenticated) {
        developer.log(
          '[DaltiProviderApp] User logged out - resetting real-time services',
        );
        RealtimeServiceInitializer.reset();
      }
    });

    return MaterialApp.router(
      title: 'Dalti Provider',
      themeMode: themeState.materialThemeMode,
      theme: themeState.lightTheme,
      darkTheme: themeState.darkTheme,
      routerConfig: router,
      debugShowCheckedModeBanner: false,

      // Localization configuration
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
        Locale('fr'), // French
        Locale('ar'), // Arabic
      ],
      locale: languageState.currentLanguage.locale,
      localeResolutionCallback: (locale, supportedLocales) {
        // If the device locale is supported, use it
        if (locale != null) {
          for (final supportedLocale in supportedLocales) {
            if (supportedLocale.languageCode == locale.languageCode) {
              return supportedLocale;
            }
          }
        }
        // Fallback to English if device locale is not supported
        return const Locale('en');
      },
    );
  }
}
