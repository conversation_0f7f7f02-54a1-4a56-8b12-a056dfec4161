import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

/// Firebase configuration - using dalti-prod for all environments
class FirebaseConfig {
  /// Get Firebase options - always use production project (dalti-prod)
  static FirebaseOptions get currentPlatform {
    return _getProductionOptions();
  }

  /// Production Firebase options
  static FirebaseOptions _getProductionOptions() {
    if (kIsWeb) {
      return const FirebaseOptions(
        apiKey: "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg",
        authDomain: "dalti-prod.firebaseapp.com",
        projectId: "dalti-prod",
        storageBucket: "dalti-prod.firebasestorage.app",
        messagingSenderId: "1060372851323",
        appId:
            "1:1060372851323:web:your_web_app_id", // You'll need to add web app to get this
      );
    }

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return const FirebaseOptions(
          apiKey: "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg",
          appId: "1:1060372851323:android:c968a0882c726c190690de",
          messagingSenderId: "1060372851323",
          projectId: "dalti-prod",
          storageBucket: "dalti-prod.firebasestorage.app",
        );
      case TargetPlatform.iOS:
        return const FirebaseOptions(
          apiKey: "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg",
          appId:
              "1:1060372851323:ios:your_ios_app_id", // You'll need to add iOS app to get this
          messagingSenderId: "1060372851323",
          projectId: "dalti-prod",
          storageBucket: "dalti-prod.firebasestorage.app",
          iosBundleId: "org.adscloud.dalti.provider",
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for this platform.',
        );
    }
  }
}
