import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../services/firebase_messaging_service.dart';
import '../services/notification_service.dart';

/// Comprehensive Android notification testing and debugging
class AndroidNotificationTest {
  static const String _testChannelId = 'dalti_provider_notifications';
  
  /// Run all Android notification tests
  static Future<void> runAllTests() async {
    if (kIsWeb) {
      print('[AndroidNotificationTest] ⚠️ Skipping Android tests on web platform');
      return;
    }

    print('\n🔍 === ANDROID NOTIFICATION DIAGNOSTIC TESTS ===\n');

    await _testPermissions();
    await _testChannelConfiguration();
    await _testFCMToken();
    await _testLocalNotification();
    await _testFirebaseMessagingSetup();
    
    print('\n✅ === ANDROID NOTIFICATION TESTS COMPLETED ===\n');
  }

  /// Test 1: Check notification permissions
  static Future<void> _testPermissions() async {
    print('📋 Test 1: Notification Permissions');
    print('─' * 40);

    try {
      // Check current permission status
      final settings = await FirebaseMessaging.instance.getNotificationSettings();
      print('Permission Status: ${settings.authorizationStatus}');
      print('Alert: ${settings.alert}');
      print('Badge: ${settings.badge}');
      print('Sound: ${settings.sound}');

      // Check if authorized
      final isAuthorized = settings.authorizationStatus == AuthorizationStatus.authorized;
      if (isAuthorized) {
        print('✅ Notifications are authorized');
      } else {
        print('❌ Notifications are NOT authorized');
        print('💡 Try: await NotificationService.requestNotificationPermissions()');
      }

      // Test permission request
      print('\n🔄 Testing permission request...');
      final requestResult = await NotificationService.requestNotificationPermissions();
      print('Permission request result: $requestResult');

    } catch (e) {
      print('❌ Permission test failed: $e');
    }
    print('');
  }

  /// Test 2: Verify notification channel configuration
  static Future<void> _testChannelConfiguration() async {
    print('📋 Test 2: Notification Channel Configuration');
    print('─' * 40);

    try {
      print('Expected Channel ID: $_testChannelId');
      print('Channel Name: Dalti Provider Notifications');
      print('Channel Description: Notifications for Dalti Provider app');
      print('Importance: High');
      print('Sound: Enabled');

      // Verify channel creation
      print('\n🔄 Testing channel creation...');
      const androidChannel = AndroidNotificationChannel(
        _testChannelId,
        'Dalti Provider Notifications',
        description: 'Notifications for Dalti Provider app',
        importance: Importance.high,
        playSound: true,
      );

      final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(androidChannel);

      print('✅ Notification channel created successfully');
      print('💡 Check Android Settings > Apps > Dalti Provider > Notifications');

    } catch (e) {
      print('❌ Channel configuration test failed: $e');
    }
    print('');
  }

  /// Test 3: Check FCM token generation and storage
  static Future<void> _testFCMToken() async {
    print('📋 Test 3: FCM Token Generation');
    print('─' * 40);

    try {
      // Test token generation
      print('🔄 Getting FCM token...');
      final token = await FirebaseMessaging.instance.getToken();
      
      if (token != null) {
        print('✅ FCM token generated successfully');
        print('Token preview: ${token.substring(0, 20)}...');
        print('Token length: ${token.length} characters');
      } else {
        print('❌ Failed to generate FCM token');
      }

      // Test stored token
      print('\n🔄 Checking stored token...');
      final storedToken = await FirebaseMessagingService.getToken();
      if (storedToken != null) {
        print('✅ Token stored locally');
        print('Stored token matches: ${token == storedToken}');
      } else {
        print('❌ No token stored locally');
      }

    } catch (e) {
      print('❌ FCM token test failed: $e');
    }
    print('');
  }

  /// Test 4: Test local notification display
  static Future<void> _testLocalNotification() async {
    print('📋 Test 4: Local Notification Display');
    print('─' * 40);

    try {
      print('🔄 Sending test local notification...');

      const androidDetails = AndroidNotificationDetails(
        _testChannelId,
        'Dalti Provider Notifications',
        channelDescription: 'Notifications for Dalti Provider app',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        icon: '@mipmap/launcher_icon',
      );

      const notificationDetails = NotificationDetails(android: androidDetails);

      final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
      await flutterLocalNotificationsPlugin.show(
        999, // Test notification ID
        'Android Notification Test',
        'This is a test notification to verify Android notification setup',
        notificationDetails,
        payload: jsonEncode({
          'type': 'test',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      print('✅ Test notification sent');
      print('💡 Check your notification tray for the test notification');

    } catch (e) {
      print('❌ Local notification test failed: $e');
    }
    print('');
  }

  /// Test 5: Verify Firebase Messaging setup
  static Future<void> _testFirebaseMessagingSetup() async {
    print('📋 Test 5: Firebase Messaging Setup');
    print('─' * 40);

    try {
      // Check if FCM service is initialized
      print('🔄 Checking Firebase Messaging initialization...');
      
      // Test message handlers
      print('Message handlers configured:');
      print('- Foreground messages: ✅ (onMessage)');
      print('- Background messages: ✅ (onBackgroundMessage)');
      print('- App opened from notification: ✅ (onMessageOpenedApp)');

      // Check background handler
      print('\n🔄 Checking background message handler...');
      print('Background handler function: firebaseMessagingBackgroundHandler');
      print('Handler location: main.dart');

      // Test token refresh
      print('\n🔄 Testing token refresh listener...');
      print('Token refresh listener: ✅ Configured');

      print('\n✅ Firebase Messaging setup verified');

    } catch (e) {
      print('❌ Firebase Messaging setup test failed: $e');
    }
    print('');
  }

  /// Send a test notification using Firebase Console
  static void printFirebaseConsoleInstructions() {
    print('\n📱 === FIREBASE CONSOLE TEST INSTRUCTIONS ===');
    print('1. Go to Firebase Console > Project > Cloud Messaging');
    print('2. Click "Send your first message"');
    print('3. Enter:');
    print('   - Title: "Test Notification"');
    print('   - Text: "Testing Android notifications"');
    print('4. Click "Send test message"');
    print('5. Paste your FCM token (from Test 3 above)');
    print('6. Click "Test"');
    print('7. Check if notification appears on your Android device');
    print('═' * 50);
  }

  /// Debug notification issues
  static Future<void> debugNotificationIssues() async {
    print('\n🔧 === NOTIFICATION TROUBLESHOOTING ===');
    
    print('\n1. Common Issues:');
    print('   ❌ Channel ID mismatch → Fixed: Using "dalti_provider_notifications"');
    print('   ❌ Icon not found → Fixed: Using "@mipmap/launcher_icon"');
    print('   ❌ Permissions denied → Check: Android Settings > Apps > Permissions');
    print('   ❌ Background restrictions → Check: Battery optimization settings');
    
    print('\n2. Android Version Specific:');
    print('   📱 Android 13+: Requires POST_NOTIFICATIONS permission');
    print('   📱 Android 12+: May have notification rate limiting');
    print('   📱 Android 8+: Requires notification channels');
    
    print('\n3. Device Specific:');
    print('   🔋 Battery optimization: May block background notifications');
    print('   🔕 Do Not Disturb: May suppress notifications');
    print('   📱 OEM modifications: Samsung, Xiaomi, etc. may have additional restrictions');
    
    print('\n4. Testing Tips:');
    print('   ✅ Test on physical device (not emulator)');
    print('   ✅ Test with app in foreground and background');
    print('   ✅ Test with fresh app install');
    print('   ✅ Check Android notification settings for the app');
  }
}
