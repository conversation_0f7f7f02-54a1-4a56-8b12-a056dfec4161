# Android Notification Issues - Diagnostic Report

## Issues Found

### 1. 🚨 **CRITICAL: Notification Channel ID Mismatch**
- **AndroidManifest.xml** specifies: `dalti_provider_notifications`
- **Code** creates/uses: `default_channel_id`
- **Impact**: Notifications may not display properly on Android

### 2. 🚨 **CRITICAL: Notification Icon Mismatch**
- **AndroidManifest.xml** specifies: `@mipmap/launcher_icon`
- **Code** uses: `@mipmap/ic_launcher`
- **Impact**: Notifications may show default icon or fail to display

### 3. ⚠️ **Potential: Background Message Handler**
- Background handler is set up in main.dart
- Need to verify it's working correctly for background notifications

### 4. ⚠️ **Potential: Notification Permissions**
- Android 13+ requires explicit POST_NOTIFICATIONS permission
- Permission is declared but need to verify runtime request

## Detailed Analysis

### Channel ID Issue
```xml
<!-- AndroidManifest.xml -->
<meta-data
    android:name="com.google.firebase.messaging.default_notification_channel_id"
    android:value="dalti_provider_notifications" />
```

```dart
// firebase_messaging_service.dart
const androidChannel = AndroidNotificationChannel(
  'default_channel_id',  // ❌ MISMATCH!
  'Default Notifications',
  // ...
);
```

### Icon Issue
```xml
<!-- AndroidManifest.xml -->
<meta-data
    android:name="com.google.firebase.messaging.default_notification_icon"
    android:resource="@mipmap/launcher_icon" />
```

```dart
// firebase_messaging_service.dart
const androidDetails = AndroidNotificationDetails(
  'default_channel_id',
  'Default Notifications',
  // ...
  icon: '@mipmap/ic_launcher',  // ❌ MISMATCH!
);
```

## Available Resources
✅ Both icons exist:
- `@mipmap/ic_launcher` (standard Flutter icon)
- `@mipmap/launcher_icon` (custom app icon)

✅ Colors defined:
- `@color/notification_color` = `#15424E`

## Recommended Fixes

### Fix 1: Align Channel IDs
Choose one consistent channel ID across all configurations.

**Option A: Use AndroidManifest.xml value**
- Update code to use `dalti_provider_notifications`

**Option B: Update AndroidManifest.xml**
- Change to `default_channel_id`

### Fix 2: Align Icon References
Choose one consistent icon reference.

**Option A: Use launcher_icon (recommended)**
- Update code to use `@mipmap/launcher_icon`

**Option B: Use ic_launcher**
- Update AndroidManifest.xml to use `@mipmap/ic_launcher`

### Fix 3: Verify Permissions
Ensure runtime permission request for Android 13+

### Fix 4: Test Background Notifications
Verify background message handler is working

## Testing Checklist

### Foreground Notifications
- [ ] App is open and in foreground
- [ ] Send test notification from Firebase Console
- [ ] Verify notification appears with correct icon and channel

### Background Notifications
- [ ] App is in background or closed
- [ ] Send test notification from Firebase Console
- [ ] Verify notification appears in system tray

### Permission Testing
- [ ] Fresh app install
- [ ] Verify permission dialog appears
- [ ] Test with permission granted/denied

### Channel Testing
- [ ] Check Android Settings > Apps > Dalti Provider > Notifications
- [ ] Verify notification channel appears correctly
- [ ] Test channel settings (sound, vibration, etc.)

## Debug Commands

### Check FCM Token
```dart
final token = await FirebaseMessaging.instance.getToken();
print('FCM Token: $token');
```

### Check Permissions
```dart
final settings = await FirebaseMessaging.instance.getNotificationSettings();
print('Permission: ${settings.authorizationStatus}');
```

### Check Channel Creation
```dart
// Add debug logs in _initializeLocalNotifications()
print('Creating notification channel: dalti_provider_notifications');
```

## Next Steps
1. Fix channel ID mismatch (highest priority)
2. Fix icon reference mismatch
3. Test notifications on physical Android device
4. Verify background message handling
5. Test on different Android versions (especially 13+)
