import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import 'notification_service.dart';
import 'notification_api_service.dart';
import 'web_notification_service.dart';
import '../storage/web_storage_service.dart';
import '../storage/auth_storage_service.dart';
import '../auth/jwt_service.dart';
import '../network/http_client.dart';
import '../network/interceptors/auth_interceptor.dart';

/// Service for handling Firebase Cloud Messaging
class FirebaseMessagingService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;
  static bool _tokenSentToServer = false;

  /// Initialize Firebase Messaging with enhanced debugging
  /// Now initializes immediately like Customer app pattern
  static Future<NotificationSettings?> initialize() async {
    if (_isInitialized) {
      print('[FCM_DEBUG] ⚠️ FCM already initialized, skipping...');
      return null;
    }

    try {
      print('[FCM_DEBUG] 🚀 Starting Firebase Messaging initialization...');
      print(
        '[FCM_DEBUG] 🔍 Called from: ${StackTrace.current.toString().split('\n')[1]}',
      );

      // Request notification permissions
      final settings = await _requestPermissions();
      print(
        '[FCM_DEBUG] 🔐 Permission status: ${settings.authorizationStatus}',
      );
      print('[FCM_DEBUG] 🔐 Alert: ${settings.alert}');
      print('[FCM_DEBUG] 🔐 Badge: ${settings.badge}');
      print('[FCM_DEBUG] 🔐 Sound: ${settings.sound}');

      // Initialize local notifications (only for mobile platforms)
      if (!kIsWeb) {
        await _initializeLocalNotifications();
        print('[FCM_DEBUG] 📱 Local notifications initialized for mobile');
      } else {
        print(
          '[FCM_DEBUG] 🌐 Web platform - using service worker notifications',
        );
      }

      // Configure message handlers with debugging
      _configureMessageHandlers();
      print('[FCM_DEBUG] 📨 Message handlers configured');

      // Configure token refresh handler
      _configureTokenRefreshHandler();
      print('[FCM_DEBUG] 🔄 Token refresh handler configured');

      // Get FCM token but don't send to server yet (wait for login)
      try {
        await _getAndStoreFcmToken();
        print('[FCM_DEBUG] ✅ FCM token obtained and stored locally');
      } catch (tokenError) {
        print('[FCM_DEBUG] ⚠️ FCM token generation failed: $tokenError');
        // Don't fail initialization if token retrieval fails
      }

      // Test notification capability
      await _testNotificationSetup();

      _isInitialized = true;
      print(
        '[FCM_DEBUG] ✅ Firebase Messaging service initialized successfully',
      );

      // Return the permission settings for external use
      return settings;
    } catch (e) {
      print('[FCM_DEBUG] ❌ Initialization error: $e');
      print('[FCM_DEBUG] ❌ Stack trace: ${StackTrace.current}');
      // Don't rethrow - allow app to continue without FCM
      _isInitialized = false;
      return null;
    }
  }

  /// Test notification setup and capabilities
  static Future<void> _testNotificationSetup() async {
    print('[FCM_DEBUG] 🧪 Testing notification setup...');

    try {
      // Check if we can get the current token
      final token = await getToken();
      if (token != null) {
        print(
          '[FCM_DEBUG] ✅ FCM token available: ${token.substring(0, 20)}...',
        );
      } else {
        print('[FCM_DEBUG] ❌ No FCM token available');
      }

      // Check platform capabilities
      if (kIsWeb) {
        print('[FCM_DEBUG] 🌐 Web platform - checking service worker...');
        // Check if service worker is registered
        print(
          '[FCM_DEBUG] 🌐 Service worker should handle background notifications',
        );
      } else {
        print(
          '[FCM_DEBUG] 📱 Mobile platform - checking local notifications...',
        );
        // Test if we can show a local notification
        await _testLocalNotification();
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ Notification test failed: $e');
    }
  }

  /// Test local notification capability (mobile only)
  static Future<void> _testLocalNotification() async {
    if (kIsWeb) return;

    try {
      print('[FCM_DEBUG] 🧪 Testing local notification...');

      // Show a test notification
      await _localNotifications.show(
        999, // Test notification ID
        'FCM Test',
        'If you see this, local notifications are working!',
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'default_channel_id',
            'Default Notifications',
            channelDescription:
                'Default notification channel for Dalti Provider',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );

      print('[FCM_DEBUG] ✅ Test local notification sent');
    } catch (e) {
      print('[FCM_DEBUG] ❌ Test local notification failed: $e');
    }
  }

  /// Request notification permissions
  static Future<NotificationSettings> _requestPermissions() async {
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    print(
      '[FirebaseMessaging] Permission status: ${settings.authorizationStatus}',
    );
    return settings;
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (!kIsWeb) {
      const androidChannel = AndroidNotificationChannel(
        'dalti_provider_notifications',
        'Dalti Provider Notifications',
        description: 'Notifications for Dalti Provider app',
        importance: Importance.high,
        playSound: true,
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(androidChannel);
    }
  }

  /// Configure message handlers with enhanced debugging
  static void _configureMessageHandlers() {
    print('[FCM_DEBUG] 🔧 Configuring message handlers...');

    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('[FCM_DEBUG] 📨 FOREGROUND message received!');
      print('[FCM_DEBUG] 📨 Message ID: ${message.messageId}');
      print('[FCM_DEBUG] 📨 From: ${message.from}');
      print('[FCM_DEBUG] 📨 Data: ${message.data}');

      if (message.notification != null) {
        print(
          '[FCM_DEBUG] 📨 Notification title: ${message.notification!.title}',
        );
        print(
          '[FCM_DEBUG] 📨 Notification body: ${message.notification!.body}',
        );
      } else {
        print('[FCM_DEBUG] 📨 No notification payload (data-only message)');
      }

      _handleForegroundMessage(message);
    });

    // Handle messages when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('[FCM_DEBUG] 📨 BACKGROUND message tap received!');
      print('[FCM_DEBUG] 📨 Message ID: ${message.messageId}');
      print('[FCM_DEBUG] 📨 Data: ${message.data}');
      _handleMessageOpenedApp(message);
    });

    // Handle messages when app is opened from terminated state
    _handleInitialMessage();

    print('[FCM_DEBUG] ✅ Message handlers configured successfully');
  }

  /// Configure token refresh handler
  static void _configureTokenRefreshHandler() {
    // Listen for token refresh events
    _firebaseMessaging.onTokenRefresh.listen((newToken) {
      print('[FirebaseMessaging] Token refreshed: $newToken');
      // TODO: Update token in backend when it changes
      _handleTokenRefresh(newToken);
    });
  }

  /// Handle token refresh
  static void _handleTokenRefresh(String newToken) async {
    print('[FirebaseMessaging] Handling token refresh: $newToken');

    // Store the new token locally
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', newToken);

      // Only send to server if user is logged in (has JWT token)
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>(
        'jwt_token',
      );
      if (tokenData != null && tokenData['access_token'] != null) {
        await _sendTokenToServer(newToken);
        print('[FirebaseMessaging] Token refresh sent to server');
      } else {
        print(
          '[FirebaseMessaging] Token refreshed but not sent to server (user not logged in)',
        );
      }
    } catch (e) {
      print('[FirebaseMessaging] Error handling token refresh: $e');
    }

    // Also notify the notification service
    NotificationService.handleTokenRefresh(newToken);
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('[FirebaseMessaging] Foreground message: ${message.messageId}');

    // Show local notification when app is in foreground (mobile only)
    // Web handles notifications differently through the service worker
    if (!kIsWeb) {
      await _showLocalNotification(message);
    } else {
      // For web, show an in-app notification
      WebNotificationService.showInAppNotification(
        title: message.notification?.title ?? 'Dalti Provider',
        body: message.notification?.body ?? 'You have a new notification',
        onTap: () => _handleNotificationNavigation(message),
      );
    }
  }

  /// Handle initial message when app is opened from terminated state
  static Future<void> _handleInitialMessage() async {
    final message = await _firebaseMessaging.getInitialMessage();
    if (message != null) {
      _handleMessageOpenedApp(message);
    }
  }

  /// Handle messages when app is opened
  static void _handleMessageOpenedApp(RemoteMessage message) {
    print('[FirebaseMessaging] Message opened app: ${message.messageId}');

    // Handle navigation based on message data
    _handleNotificationNavigation(message);
  }

  /// Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      const androidDetails = AndroidNotificationDetails(
        'dalti_provider_notifications',
        'Dalti Provider Notifications',
        channelDescription: 'Notifications for Dalti Provider app',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        icon: '@mipmap/launcher_icon',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        notificationDetails,
        payload: jsonEncode(data),
      );
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    print('[FirebaseMessaging] Notification tapped: ${response.payload}');

    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        _handleNotificationNavigation(RemoteMessage(data: data));
      } catch (e) {
        print('[FirebaseMessaging] Error parsing notification payload: $e');
      }
    }
  }

  /// Handle notification navigation
  static void _handleNotificationNavigation(RemoteMessage message) {
    final data = message.data;

    // Handle different notification types
    final type = data['type'];

    switch (type) {
      case 'appointment':
        _navigateToAppointment(data['appointmentId']);
        break;
      case 'message':
        _navigateToMessages(data['conversationId']);
        break;
      case 'booking':
        _navigateToBooking(data['bookingId']);
        break;
      default:
        _navigateToHome();
    }
  }

  /// Navigate to appointment
  static void _navigateToAppointment(String? appointmentId) {
    print('[FirebaseMessaging] Navigate to appointment: $appointmentId');
    // TODO: Implement navigation to appointment screen
  }

  /// Navigate to messages
  static void _navigateToMessages(String? conversationId) {
    print('[FirebaseMessaging] Navigate to messages: $conversationId');
    // TODO: Implement navigation to messages screen
  }

  /// Navigate to booking
  static void _navigateToBooking(String? bookingId) {
    print('[FirebaseMessaging] Navigate to booking: $bookingId');
    // TODO: Implement navigation to booking screen
  }

  /// Navigate to home
  static void _navigateToHome() {
    print('[FirebaseMessaging] Navigate to home');
    // TODO: Implement navigation to home screen
  }

  /// Get and store FCM token locally (don't send to server yet)
  static Future<void> _getAndStoreFcmToken() async {
    try {
      print('[FCM_DEBUG] 🎫 Starting FCM token generation...');

      // Check if we have a stored token first
      final prefs = await SharedPreferences.getInstance();
      String? storedToken = prefs.getString('fcm_token');

      if (storedToken != null && storedToken.isNotEmpty) {
        print(
          '[FCM_DEBUG] 🎫 Found stored FCM Token: ${storedToken.substring(0, 20)}...',
        );
        // Token already exists, no need to regenerate
        return;
      }

      print('[FCM_DEBUG] 🎫 No stored token found, requesting new token...');

      // Get new token
      String? newToken;

      if (kIsWeb) {
        print('[FCM_DEBUG] 🌐 Requesting web FCM token...');
        // For web, try to get token without VAPID key first
        try {
          newToken = await _firebaseMessaging.getToken();
          print(
            '[FCM_DEBUG] ✅ New FCM Token (web): ${newToken?.substring(0, 20)}...',
          );
        } catch (e) {
          print('[FCM_DEBUG] ❌ Web FCM token failed: $e');
          print('[FCM_DEBUG] ⚠️ Note: VAPID key may be needed for production');
          return;
        }
      } else {
        print('[FCM_DEBUG] 📱 Requesting mobile FCM token...');
        // For mobile platforms
        newToken = await _firebaseMessaging.getToken();
        print(
          '[FCM_DEBUG] ✅ New FCM Token (mobile): ${newToken?.substring(0, 20)}...',
        );
      }

      if (newToken != null) {
        print('[FCM_DEBUG] 💾 Storing token locally...');
        // Store the token locally
        await prefs.setString('fcm_token', newToken);
        print(
          '[FCM_DEBUG] ✅ Token stored locally (will send to server after login)',
        );
      } else {
        print('[FCM_DEBUG] ❌ Failed to get new FCM token from Firebase');
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ Error in _getAndStoreFcmToken: $e');
      print('[FCM_DEBUG] ❌ Stack trace: ${StackTrace.current}');
    }
  }

  /// Send FCM token to server with enhanced debugging
  /// Uses NotificationService which has proper authentication handling
  static Future<void> _sendTokenToServer(String token) async {
    try {
      print('[FCM_DEBUG] 🌐 Preparing to send token to server...');

      // Use NotificationService which already has access to authenticated NotificationApiService
      // This avoids circular dependency issues and uses the established authentication pattern
      final success = await _sendTokenViaNotificationService(token);

      if (success) {
        print('[FCM_DEBUG] ✅ FCM token saved to server successfully!');
      } else {
        print('[FCM_DEBUG] ❌ Failed to save FCM token to server');

        // Fallback: Try direct approach with better error handling
        print(
          '[FCM_DEBUG] 🔄 Attempting fallback with improved token retrieval...',
        );
        await _sendTokenDirectWithFallback(token);
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ Error sending FCM token to server: $e');
      print('[FCM_DEBUG] ❌ Stack trace: ${StackTrace.current}');
    }
  }

  /// Send token via NotificationService (preferred method)
  static Future<bool> _sendTokenViaNotificationService(String token) async {
    try {
      // Check if NotificationService has an API service instance
      if (NotificationService.hasApiService) {
        print(
          '[FCM_DEBUG] ✅ Using NotificationService with authenticated API service',
        );
        return await NotificationService.saveFcmTokenDirect(token);
      } else {
        print('[FCM_DEBUG] ⚠️ NotificationService API service not available');
        return false;
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ Error using NotificationService: $e');
      return false;
    }
  }

  /// Fallback method with improved token retrieval for Android
  static Future<void> _sendTokenDirectWithFallback(String token) async {
    try {
      print(
        '[FCM_DEBUG] 🔄 Using fallback method with improved token retrieval...',
      );

      // Try multiple storage sources for authentication token
      String? accessToken = await _getAccessTokenFromMultipleSources();

      if (accessToken == null) {
        print('[FCM_DEBUG] ❌ No access token found in any storage source');
        return;
      }

      print(
        '[FCM_DEBUG] ✅ Access token found: ${accessToken.substring(0, 10)}...',
      );

      // Proceed with direct HTTP call
      await _sendTokenDirectHttp(token, accessToken);
    } catch (e) {
      print('[FCM_DEBUG] ❌ Error in fallback method: $e');
    }
  }

  /// Try multiple storage sources to get access token (Android-specific fix)
  static Future<String?> _getAccessTokenFromMultipleSources() async {
    print('[FCM_DEBUG] 🔍 Trying multiple storage sources for access token...');

    // Method 1: Try AuthStorageService (most reliable for Android)
    try {
      print('[FCM_DEBUG] 📱 Trying AuthStorageService...');
      final accessToken = await AuthStorageService.getAccessToken();
      if (accessToken != null && accessToken.isNotEmpty) {
        print('[FCM_DEBUG] ✅ Found token in AuthStorageService');
        return accessToken;
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ AuthStorageService failed: $e');
    }

    // Method 2: Try WebStorageService (original method)
    try {
      print('[FCM_DEBUG] 🌐 Trying WebStorageService...');
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>(
        'jwt_token',
      );
      if (tokenData != null) {
        final accessToken = tokenData['access_token'] as String?;
        if (accessToken != null && accessToken.isNotEmpty) {
          print('[FCM_DEBUG] ✅ Found token in WebStorageService');
          return accessToken;
        }
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ WebStorageService failed: $e');
    }

    // Method 3: Try SharedPreferences directly (Android fallback)
    try {
      print('[FCM_DEBUG] 📱 Trying SharedPreferences directly...');
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token');
      if (accessToken != null && accessToken.isNotEmpty) {
        print('[FCM_DEBUG] ✅ Found token in SharedPreferences');
        return accessToken;
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ SharedPreferences failed: $e');
    }

    print('[FCM_DEBUG] ❌ No access token found in any storage source');
    return null;
  }

  /// Send token via direct HTTP call with proper error handling
  static Future<void> _sendTokenDirectHttp(
    String token,
    String accessToken,
  ) async {
    try {
      print('[FCM_DEBUG] 📤 Sending token via direct HTTP call...');

      String deviceType;
      if (kIsWeb) {
        deviceType = 'web';
      } else if (Platform.isAndroid) {
        deviceType = 'android';
      } else if (Platform.isIOS) {
        deviceType = 'ios';
      } else {
        print('[FCM_DEBUG] ❌ Unsupported platform for FCM token saving.');
        return;
      }

      const String apiUrl =
          'https://dapi-test.adscloud.org:8443/api/auth/notifications/mobile/save-fcm-token';
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };
      final Map<String, String> body = {
        'token': token,
        'deviceType': deviceType,
      };

      print('[FCM_DEBUG] 📤 Sending request to: $apiUrl');
      print('[FCM_DEBUG] 📤 Headers: ${headers.keys.join(', ')}');
      print(
        '[FCM_DEBUG] 📤 Body: {token: ${token.substring(0, 20)}..., deviceType: $deviceType}',
      );

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: headers,
        body: jsonEncode(body),
      );

      print('[FCM_DEBUG] 📥 Response status: ${response.statusCode}');
      print('[FCM_DEBUG] 📥 Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        print(
          '[FCM_DEBUG] ✅ FCM token saved to server successfully via direct HTTP!',
        );
      } else {
        print(
          '[FCM_DEBUG] ❌ Failed to save FCM token to server via direct HTTP',
        );
        print('[FCM_DEBUG] ❌ Status: ${response.statusCode}');
        print('[FCM_DEBUG] ❌ Body: ${response.body}');
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ Error in direct HTTP call: $e');
    }
  }

  /// Get current FCM token
  static Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('fcm_token');
    } catch (e) {
      print('[FirebaseMessaging] Error getting stored token: $e');
      return null;
    }
  }

  /// Request permissions and return the settings
  static Future<NotificationSettings> requestPermissions() async {
    return await _requestPermissions();
  }

  /// Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      print('[FirebaseMessaging] Subscribed to topic: $topic');
    } catch (e) {
      print('[FirebaseMessaging] Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('[FirebaseMessaging] Unsubscribed from topic: $topic');
    } catch (e) {
      print('[FirebaseMessaging] Error unsubscribing from topic $topic: $e');
    }
  }

  /// Handle background messages (must be top-level function)
  static Future<void> handleBackgroundMessage(RemoteMessage message) async {
    print('[FirebaseMessaging] Background message: ${message.messageId}');
    // Handle background message processing here
  }

  /// Send stored FCM token to server (call after login)
  static Future<void> sendTokenToServerAfterLogin() async {
    try {
      print('[FCM_DEBUG] 🔄 Sending stored FCM token to server after login...');

      final token = await getToken();
      if (token != null) {
        await _sendTokenToServer(token);
        _tokenSentToServer = true;
        print(
          '[FCM_DEBUG] ✅ FCM token sent to server successfully after login',
        );
      } else {
        print('[FCM_DEBUG] ❌ No FCM token available to send to server');
      }
    } catch (e) {
      print('[FCM_DEBUG] ❌ Error sending FCM token to server after login: $e');
    }
  }

  /// Re-register FCM token with server (call after login) - Legacy method
  static Future<void> reRegisterToken() async {
    await sendTokenToServerAfterLogin();
  }

  /// Manual debugging method - call this to test FCM setup
  static Future<void> debugFCMSetup() async {
    print('[FCM_DEBUG] 🔍 === FCM DEBUGGING SESSION ===');

    try {
      // Check if FCM is initialized
      print('[FCM_DEBUG] 🔍 FCM Initialized: $_isInitialized');

      // Check current token
      final token = await getToken();
      if (token != null) {
        print('[FCM_DEBUG] 🔍 Current Token: ${token.substring(0, 30)}...');
      } else {
        print('[FCM_DEBUG] 🔍 No token available');
      }

      // Check permissions
      final settings = await _firebaseMessaging.getNotificationSettings();
      print(
        '[FCM_DEBUG] 🔍 Permission Status: ${settings.authorizationStatus}',
      );
      print('[FCM_DEBUG] 🔍 Alert: ${settings.alert}');
      print('[FCM_DEBUG] 🔍 Badge: ${settings.badge}');
      print('[FCM_DEBUG] 🔍 Sound: ${settings.sound}');

      // Check JWT token
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>(
        'jwt_token',
      );
      if (tokenData != null && tokenData['access_token'] != null) {
        final accessToken = tokenData['access_token'] as String;
        print('[FCM_DEBUG] 🔍 JWT Token: ${accessToken.substring(0, 10)}...');
      } else {
        print('[FCM_DEBUG] 🔍 No JWT token found');
      }

      print('[FCM_DEBUG] 🔍 === END DEBUGGING SESSION ===');
    } catch (e) {
      print('[FCM_DEBUG] ❌ Debug session error: $e');
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await FirebaseMessagingService.handleBackgroundMessage(message);
}
