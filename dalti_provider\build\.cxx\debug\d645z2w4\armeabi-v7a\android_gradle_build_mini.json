{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\.cxx\\debug\\d645z2w4\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\.cxx\\debug\\d645z2w4\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}