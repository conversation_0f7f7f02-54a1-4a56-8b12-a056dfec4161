import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'firebase_messaging_service.dart';
import 'notification_api_service.dart';

/// Service for managing notification-related operations
class NotificationService {
  static bool _isInitialized = false;
  static NotificationApiService? _apiService;

  /// Check if API service is available
  static bool get hasApiService => _apiService != null;

  /// Save FCM token directly using the API service
  static Future<bool> saveFcmTokenDirect(String token) async {
    if (_apiService == null) {
      print(
        '[NotificationService] API service not available for FCM token save',
      );
      return false;
    }

    try {
      return await _apiService!.saveFcmToken(fcmToken: token);
    } catch (e) {
      print('[NotificationService] Error saving FCM token directly: $e');
      return false;
    }
  }

  /// Initialize notification service (without FCM - FCM initializes after login)
  static Future<void> initialize({NotificationApiService? apiService}) async {
    if (_isInitialized) return;

    try {
      // Store API service reference
      _apiService = apiService;

      // Note: Firebase Messaging will be initialized after login
      print(
        '[NotificationService] Service initialized (FCM will initialize after login)',
      );

      _isInitialized = true;
      print('[NotificationService] Service initialized successfully');
    } catch (e) {
      print('[NotificationService] Initialization error: $e');
    }
  }

  /// Get FCM token for the current device
  static Future<String?> getDeviceToken() async {
    try {
      return await FirebaseMessagingService.getToken();
    } catch (e) {
      print('[NotificationService] Error getting device token: $e');
      return null;
    }
  }

  /// Subscribe to provider-specific topics
  static Future<void> subscribeToProviderTopics(String providerId) async {
    try {
      final topics = [
        'provider_$providerId',
        'appointments',
        'bookings',
        'messages',
        'general',
      ];

      for (final topic in topics) {
        await FirebaseMessagingService.subscribeToTopic(topic);
      }

      print(
        '[NotificationService] Subscribed to provider topics for: $providerId',
      );
    } catch (e) {
      print('[NotificationService] Error subscribing to provider topics: $e');
    }
  }

  /// Unsubscribe from provider-specific topics
  static Future<void> unsubscribeFromProviderTopics(String providerId) async {
    try {
      final topics = [
        'provider_$providerId',
        'appointments',
        'bookings',
        'messages',
        'general',
      ];

      for (final topic in topics) {
        await FirebaseMessagingService.unsubscribeFromTopic(topic);
      }

      print(
        '[NotificationService] Unsubscribed from provider topics for: $providerId',
      );
    } catch (e) {
      print(
        '[NotificationService] Error unsubscribing from provider topics: $e',
      );
    }
  }

  /// Subscribe to a specific topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await FirebaseMessagingService.subscribeToTopic(topic);
      print('[NotificationService] Subscribed to topic: $topic');
    } catch (e) {
      print('[NotificationService] Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from a specific topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await FirebaseMessagingService.unsubscribeFromTopic(topic);
      print('[NotificationService] Unsubscribed from topic: $topic');
    } catch (e) {
      print('[NotificationService] Error unsubscribing from topic $topic: $e');
    }
  }

  /// Send device token to backend for user-specific notifications
  static Future<bool> registerDeviceToken(String userId, String token) async {
    try {
      if (_apiService != null) {
        final success = await _apiService!.saveFcmToken(
          fcmToken: token,
          userId: userId,
        );

        if (success) {
          print(
            '[NotificationService] Device token registered for user: $userId',
          );
        } else {
          print(
            '[NotificationService] Failed to register device token for user: $userId',
          );
        }

        return success;
      } else {
        print(
          '[NotificationService] API service not available, skipping token registration',
        );
        return false;
      }
    } catch (e) {
      print('[NotificationService] Error registering device token: $e');
      return false;
    }
  }

  /// Remove device token from backend
  static Future<bool> unregisterDeviceToken(String userId, String token) async {
    try {
      if (_apiService != null) {
        final success = await _apiService!.removeFcmToken(
          fcmToken: token,
          userId: userId,
        );

        if (success) {
          print(
            '[NotificationService] Device token unregistered for user: $userId',
          );
        } else {
          print(
            '[NotificationService] Failed to unregister device token for user: $userId',
          );
        }

        return success;
      } else {
        print(
          '[NotificationService] API service not available, skipping token unregistration',
        );
        return false;
      }
    } catch (e) {
      print('[NotificationService] Error unregistering device token: $e');
      return false;
    }
  }

  /// Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    try {
      final settings = await FirebaseMessaging.instance
          .getNotificationSettings();
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      print('[NotificationService] Error checking notification status: $e');
      return false;
    }
  }

  /// Handle FCM token refresh
  static Future<void> handleTokenRefresh(String newToken) async {
    try {
      print('[NotificationService] Handling token refresh: $newToken');

      // If we have an API service and a current user, update the token
      if (_apiService != null) {
        // Try to get current user ID from storage or auth state
        // This is a simplified approach - in a real app you might want to
        // get the user ID from a more reliable source
        final success = await _apiService!.saveFcmToken(fcmToken: newToken);

        if (success) {
          print('[NotificationService] FCM token updated successfully');
        } else {
          print('[NotificationService] Failed to update FCM token');
        }
      } else {
        print(
          '[NotificationService] API service not available, token refresh skipped',
        );
      }
    } catch (e) {
      print('[NotificationService] Error handling token refresh: $e');
    }
  }

  /// Request notification permissions and save token if granted
  static Future<bool> requestNotificationPermissions({String? userId}) async {
    try {
      final settings = await FirebaseMessagingService.requestPermissions();
      final isAuthorized =
          settings.authorizationStatus == AuthorizationStatus.authorized;

      // If permissions are granted and we have API service, save the token
      if (isAuthorized && _apiService != null) {
        final token = await FirebaseMessagingService.getToken();
        if (token != null) {
          await _apiService!.saveFcmToken(fcmToken: token, userId: userId);
          print('[NotificationService] FCM token saved after permission grant');
        }
      }

      return isAuthorized;
    } catch (e) {
      print(
        '[NotificationService] Error requesting notification permissions: $e',
      );
      return false;
    }
  }
}
