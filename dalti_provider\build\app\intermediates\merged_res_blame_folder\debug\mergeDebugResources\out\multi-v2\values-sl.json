{"logs": [{"outputFile": "org.adscloud.dalti.provider.app-mergeDebugResources-57:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b9ebe0379b63cf1a9d593989bc44e09a\\transformed\\browser-1.8.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5863,6055,6159,6271", "endColumns": "105,103,111,101", "endOffsets": "5964,6154,6266,6368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37fe99c3dffcb6c3e42d067184fcf556\\transformed\\core-1.16.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2942,3044,3142,3246,3349,3451,6679", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "2937,3039,3137,3241,3344,3446,3563,6775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bfa26447f798317d7f09bc1dbacdadf8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3568,3674,3839,3968,4075,4222,4352,4466,4710,4868,4975,5142,5272,5424,5576,5646,5708", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "3669,3834,3963,4070,4217,4347,4461,4565,4863,4970,5137,5267,5419,5571,5641,5703,5787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d0fda4cc076273a602c8713a382688ca\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4570", "endColumns": "139", "endOffsets": "4705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f35be5762b9bd4ab0c6bb255f6ea5c4a\\transformed\\preference-1.2.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,343,484,653,741", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "171,257,338,479,648,736,820"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5792,5969,6373,6454,6780,6949,7037", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "5858,6050,6449,6590,6944,7032,7116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\87617f28411ca702fb74dfb3b4d42b44\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,6595", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,6674"}}]}]}