<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res"><file name="launch_background" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="launch_background" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-hdpi\launcher_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-mdpi\launcher_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-xhdpi\launcher_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-xxhdpi\launcher_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\mipmap-xxxhdpi\launcher_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_color">#15424E</color><color name="notification_color">#15424E</color><color name="accent_color">#2A9D8F</color><color name="status_bar_color">#15424E</color><color name="background_light">#F7F5F2</color><color name="background_dark">#264653</color><color name="provider_primary">#15424E</color><color name="provider_secondary">#2A9D8F</color></file><file path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="data_extraction_rules" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\resValues\debug"/><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\resValues\debug"/><source path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\processDebugGoogleServices"><file path="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">816987655237</string><string name="google_api_key" translatable="false">AIzaSyCLV3ESOoycIrloOQ3IxZbOesUcfc8kJpY</string><string name="google_app_id" translatable="false">1:816987655237:android:5c8650ead20f8cb12e672c</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCLV3ESOoycIrloOQ3IxZbOesUcfc8kJpY</string><string name="google_storage_bucket" translatable="false">dalti-3d06b.firebasestorage.app</string><string name="project_id" translatable="false">dalti-3d06b</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>