import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import '../config/firebase_config.dart';
import '../services/firebase_messaging_service.dart';

/// Firebase configuration verification and testing
class FirebaseConfigTest {
  
  /// Run comprehensive Firebase configuration tests
  static Future<void> runAllTests() async {
    print('\n🔍 === FIREBASE CONFIGURATION VERIFICATION ===\n');

    await _testFirebaseInitialization();
    await _testProjectConfiguration();
    await _testFCMConfiguration();
    await _testTokenGeneration();
    
    print('\n✅ === FIREBASE CONFIGURATION TESTS COMPLETED ===\n');
  }

  /// Test 1: Firebase initialization
  static Future<void> _testFirebaseInitialization() async {
    print('📋 Test 1: Firebase Initialization');
    print('─' * 40);

    try {
      // Check if Firebase is initialized
      if (Firebase.apps.isNotEmpty) {
        final app = Firebase.app();
        print('✅ Firebase is initialized');
        print('App name: ${app.name}');
        print('Project ID: ${app.options.projectId}');
        print('Sender ID: ${app.options.messagingSenderId}');
        print('App ID: ${app.options.appId}');
        
        // Verify it's using dalti-prod
        if (app.options.projectId == 'dalti-prod') {
          print('✅ Using correct project: dalti-prod');
        } else {
          print('❌ Wrong project: ${app.options.projectId} (expected: dalti-prod)');
        }
      } else {
        print('❌ Firebase is not initialized');
      }

    } catch (e) {
      print('❌ Firebase initialization test failed: $e');
    }
    print('');
  }

  /// Test 2: Project configuration verification
  static Future<void> _testProjectConfiguration() async {
    print('📋 Test 2: Project Configuration');
    print('─' * 40);

    try {
      final config = FirebaseConfig.currentPlatform;
      
      print('Current platform configuration:');
      print('Project ID: ${config.projectId}');
      print('Sender ID: ${config.messagingSenderId}');
      print('App ID: ${config.appId}');
      print('API Key: ${config.apiKey.substring(0, 10)}...');
      print('Storage Bucket: ${config.storageBucket}');
      
      // Platform-specific checks
      if (kIsWeb) {
        print('Platform: Web');
        print('Auth Domain: ${config.authDomain}');
      } else {
        print('Platform: ${defaultTargetPlatform.name}');
        if (config.iosBundleId != null) {
          print('iOS Bundle ID: ${config.iosBundleId}');
        }
      }

      // Verify dalti-prod configuration
      final expectedValues = {
        'projectId': 'dalti-prod',
        'messagingSenderId': '1060372851323',
        'apiKey': 'AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg',
        'storageBucket': 'dalti-prod.firebasestorage.app',
      };

      bool allCorrect = true;
      for (final entry in expectedValues.entries) {
        final key = entry.key;
        final expected = entry.value;
        String? actual;
        
        switch (key) {
          case 'projectId':
            actual = config.projectId;
            break;
          case 'messagingSenderId':
            actual = config.messagingSenderId;
            break;
          case 'apiKey':
            actual = config.apiKey;
            break;
          case 'storageBucket':
            actual = config.storageBucket;
            break;
        }

        if (actual == expected) {
          print('✅ $key: $actual');
        } else {
          print('❌ $key: $actual (expected: $expected)');
          allCorrect = false;
        }
      }

      if (allCorrect) {
        print('\n✅ All configuration values are correct for dalti-prod');
      } else {
        print('\n❌ Some configuration values are incorrect');
      }

    } catch (e) {
      print('❌ Project configuration test failed: $e');
    }
    print('');
  }

  /// Test 3: FCM configuration
  static Future<void> _testFCMConfiguration() async {
    print('📋 Test 3: FCM Configuration');
    print('─' * 40);

    try {
      // Check FCM instance
      final messaging = FirebaseMessaging.instance;
      print('✅ FCM instance accessible');

      // Check notification settings
      final settings = await messaging.getNotificationSettings();
      print('Permission status: ${settings.authorizationStatus}');
      print('Alert: ${settings.alert}');
      print('Badge: ${settings.badge}');
      print('Sound: ${settings.sound}');

      // Check if permissions are granted
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('✅ Notifications are authorized');
      } else {
        print('⚠️ Notifications are not authorized');
        print('💡 Run: await FirebaseMessaging.instance.requestPermission()');
      }

    } catch (e) {
      print('❌ FCM configuration test failed: $e');
    }
    print('');
  }

  /// Test 4: Token generation and verification
  static Future<void> _testTokenGeneration() async {
    print('📋 Test 4: FCM Token Generation');
    print('─' * 40);

    try {
      // Generate FCM token
      print('🔄 Generating FCM token...');
      final token = await FirebaseMessaging.instance.getToken();
      
      if (token != null) {
        print('✅ FCM token generated successfully');
        print('Token preview: ${token.substring(0, 30)}...');
        print('Token length: ${token.length} characters');
        
        // Verify token format for dalti-prod
        if (token.contains('1:1060372851323:')) {
          print('✅ Token format is correct for dalti-prod project');
        } else if (token.contains('1:816987655237:')) {
          print('❌ Token is for dalti-3d06b project (old dev project)');
          print('💡 This indicates the app is still using the wrong Firebase project');
        } else {
          print('⚠️ Token format is unrecognized');
        }

        // Test stored token
        final storedToken = await FirebaseMessagingService.getToken();
        if (storedToken != null) {
          print('✅ Token is stored locally');
          print('Stored token matches: ${token == storedToken}');
        } else {
          print('⚠️ Token is not stored locally yet');
        }

      } else {
        print('❌ Failed to generate FCM token');
        print('💡 Check Firebase configuration and network connectivity');
      }

    } catch (e) {
      print('❌ Token generation test failed: $e');
    }
    print('');
  }

  /// Print Firebase Console URLs for testing
  static void printFirebaseConsoleInfo() {
    print('\n📱 === FIREBASE CONSOLE TESTING INFO ===');
    print('');
    print('🔗 Firebase Console URLs:');
    print('Production Project: https://console.firebase.google.com/project/dalti-prod');
    print('Cloud Messaging: https://console.firebase.google.com/project/dalti-prod/messaging');
    print('');
    print('📋 Testing Steps:');
    print('1. Go to Cloud Messaging in Firebase Console');
    print('2. Click "Send your first message"');
    print('3. Enter test title and message');
    print('4. Click "Send test message"');
    print('5. Use the FCM token from Test 4 above');
    print('6. Click "Test" to send notification');
    print('');
    print('🎯 Expected Results:');
    print('- Notification should appear on your Android device');
    print('- Check both foreground and background scenarios');
    print('- Verify notification icon and channel are correct');
    print('═' * 50);
  }

  /// Debug configuration mismatches
  static void debugConfigurationIssues() {
    print('\n🔧 === CONFIGURATION TROUBLESHOOTING ===');
    print('');
    print('Common Issues and Solutions:');
    print('');
    print('❌ Token format shows wrong project:');
    print('   → Check Firebase initialization in main.dart');
    print('   → Verify FirebaseConfig.currentPlatform returns dalti-prod');
    print('   → Clear app data and reinstall');
    print('');
    print('❌ Notifications not received:');
    print('   → Verify sending to correct Firebase project (dalti-prod)');
    print('   → Check notification permissions');
    print('   → Verify notification channel configuration');
    print('');
    print('❌ 401 errors when saving token:');
    print('   → Check user authentication status');
    print('   → Verify API endpoint configuration');
    print('   → Check network connectivity');
    print('');
    print('❌ iOS configuration missing:');
    print('   → Add iOS app to dalti-prod Firebase project');
    print('   → Update iOS app ID in FirebaseConfig');
    print('   → Generate and download GoogleService-Info.plist');
    print('═' * 50);
  }
}
