# Android FCM Token Save Authentication Fix

## Problem Summary
The FCM token save functionality was failing on Android with a 401 unauthorized error when calling the save-fcm-token endpoint, while the same functionality worked correctly on iOS.

## Root Cause Analysis
The issue was in the `FirebaseMessagingService._sendTokenToServer()` method which used direct HTTP calls with manual token retrieval from `WebStorageService`. The problem occurred because:

1. **Platform-specific storage differences**: 
   - `WebStorageService` uses **SharedPreferences** for web and **Hive** for mobile platforms
   - On Android, if Hive initialization failed or the auth box wasn't properly opened, `WebStorageService.getAuth()` returned `null`
   - This caused the access token to not be found, resulting in a 401 error

2. **Inconsistent authentication patterns**:
   - The codebase had two different FCM token saving implementations:
     - `FirebaseMessagingService._sendTokenToServer()` - Direct HTTP calls with manual token retrieval
     - `NotificationApiService.saveFcmToken()` - HttpClient with AuthInterceptor for automatic authentication

## Solution Implemented

### 1. Enhanced Authentication Strategy
Modified `FirebaseMessagingService._sendTokenToServer()` to use a **layered approach**:

1. **Primary Method**: Use `NotificationService` with authenticated `NotificationApiService`
   - Leverages the existing HttpClient with AuthInterceptor
   - Avoids circular dependency issues
   - Uses the established authentication pattern

2. **Fallback Method**: Multi-source token retrieval for Android
   - Tries `AuthStorageService` (most reliable for Android)
   - Falls back to `WebStorageService` (original method)
   - Final fallback to `SharedPreferences` directly

### 2. Code Changes

#### A. Enhanced NotificationService (`notification_service.dart`)
```dart
/// Check if API service is available
static bool get hasApiService => _apiService != null;

/// Save FCM token directly using the API service
static Future<bool> saveFcmTokenDirect(String token) async {
  if (_apiService == null) {
    return false;
  }
  return await _apiService!.saveFcmToken(fcmToken: token);
}
```

#### B. Improved FirebaseMessagingService (`firebase_messaging_service.dart`)
```dart
/// Send FCM token to server with enhanced debugging
/// Uses NotificationService which has proper authentication handling
static Future<void> _sendTokenToServer(String token) async {
  // Primary: Use NotificationService with authenticated API service
  final success = await _sendTokenViaNotificationService(token);
  
  if (!success) {
    // Fallback: Try direct approach with better error handling
    await _sendTokenDirectWithFallback(token);
  }
}
```

#### C. Multi-source Token Retrieval
```dart
/// Try multiple storage sources to get access token (Android-specific fix)
static Future<String?> _getAccessTokenFromMultipleSources() async {
  // Method 1: Try AuthStorageService (most reliable for Android)
  // Method 2: Try WebStorageService (original method)  
  // Method 3: Try SharedPreferences directly (Android fallback)
}
```

### 3. Key Benefits

1. **Platform Compatibility**: Works reliably on both Android and iOS
2. **Backward Compatibility**: Doesn't break existing iOS functionality
3. **Robust Fallback**: Multiple authentication token sources for Android
4. **Consistent Patterns**: Uses established authentication mechanisms
5. **Better Error Handling**: Enhanced debugging and error reporting

### 4. Testing Recommendations

1. **Android Testing**:
   - Test FCM token save after login on Android devices
   - Verify 401 errors are resolved
   - Check debug logs for successful token retrieval

2. **iOS Testing**:
   - Ensure existing iOS functionality remains unaffected
   - Verify FCM token save continues to work

3. **Cross-platform Testing**:
   - Test login/logout cycles on both platforms
   - Verify FCM token refresh scenarios
   - Test app restart scenarios

### 5. Debug Logging
Enhanced debug logging helps identify which authentication method succeeded:
- `[FCM_DEBUG] ✅ Using NotificationService with authenticated API service`
- `[FCM_DEBUG] ✅ Found token in AuthStorageService`
- `[FCM_DEBUG] ✅ Found token in WebStorageService`
- `[FCM_DEBUG] ✅ Found token in SharedPreferences`

## Files Modified
1. `dalti_provider/lib/core/services/firebase_messaging_service.dart`
2. `dalti_provider/lib/core/services/notification_service.dart`

## Implementation Notes
- The fix maintains the existing provider pattern and doesn't introduce circular dependencies
- Uses the same authentication mechanisms that work for other API calls
- Provides comprehensive fallback mechanisms specifically for Android platform issues
- Enhanced debugging helps identify which storage method works on different devices
