"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\file_picker\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\flutter_plugin_android_lifecycle\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77a57776ade161208265d2b8c7b03d2c\\transformed\\jetified-flutter_embedding_debug-1.0.0-a8bfdfc394deaed5c57bd45a64ac4294dc976a72.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d2fc16653ff98449e5afda5923b5294\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ecbcc9dbce6fb0fe70316be126fefcdc\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4e8891bc8975cd0da761148d12055588\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\35dee563440a7999b75f3a47980f0843\\transformed\\jetified-lifecycle-livedata-core-ktx-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2f27041ee4b2e55aad1f9168a769d4f\\transformed\\lifecycle-livedata-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4c5f0c769797cb81b286cc9de8d5c174\\transformed\\lifecycle-viewmodel-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca7b3b5167fc874cc4b8314f538c771c\\transformed\\jetified-lifecycle-viewmodel-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3fcbaf1a8d27d6f7456a41b0d9c18ffb\\transformed\\lifecycle-livedata-core-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e55dc8cc67256f332a7bfcb9f739aaed\\transformed\\jetified-lifecycle-common-jvm-2.8.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9dc3b38361ede65349ab2efca74e1855\\transformed\\jetified-lifecycle-runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.8.7\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.8.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6cd47cd971f73c2f6e3f7cf7fe35406f\\transformed\\jetified-lifecycle-process-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d7ef743d704f17319b08f6c09a8ea780\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c7b644a509695ffe8579ada470379a77\\transformed\\jetified-core-ktx-1.15.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\05891960b183a9002606bccd729cfe8b\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2ebdd5993b75eb6e23e26ce2965fb23\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\63d9fd9318b3f6146bac29030a02715d\\transformed\\core-1.15.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f1f60f2a559aa68f208bd72232f85694\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e59fefeb2ef861f1050d323105c3f7d2\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\66b0ccfa6b094f3466bbd551345ed670\\transformed\\jetified-annotation-experimental-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\26dd23b1db5107ea2a6b2c998df02a06\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c2947395e8fab9a91193e5f88065cea3\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ec3eb1e95c63b36e433bc3002b38324\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\782a6a089ae8dbb0c4cd4d6071a1c668\\transformed\\jetified-collection-jvm-1.4.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5923bc58afc0a24509a7d927bc51ad92\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0c81cd42481197d80547d471a2a7238\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a53240c83d227dd7d48a45bf800cdb21\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f610fcac379a581a5f8497542d8bc04b\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb2c46fb12030802b25931c1e7cc6c27\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6df012aed8a88dd0a8d20285d34538d6\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f857550d659784060d9f7c1a98442f1b\\transformed\\jetified-tika-core-3.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8521e63fa3973b03a64640ba8087db9c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d531023fd29aeab28529d21bfc03a0d\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec6b4f7eedded958dc57536e52e45c61\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7d3cf6280bab24c0706cae5087f7396\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b43d91b63b5c2aa1dd735d6fe4431b2\\transformed\\jetified-slf4j-api-2.0.17.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9dbb147f8c2b1db0154e6ee03182a88a\\transformed\\jetified-commons-io-2.19.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\file_picker\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "file_picker_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-10.3.2\\android\\src\\main\\kotlin\\com\\mr\\flutter\\plugin\\filepicker\\FileInfo.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-10.3.2\\android\\src\\main\\kotlin\\com\\mr\\flutter\\plugin\\filepicker\\FilePickerDelegate.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-10.3.2\\android\\src\\main\\kotlin\\com\\mr\\flutter\\plugin\\filepicker\\FilePickerPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-10.3.2\\android\\src\\main\\kotlin\\com\\mr\\flutter\\plugin\\filepicker\\FileUtils.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-10.3.2\\android\\src\\main\\kotlin\\com\\mr\\flutter\\plugin\\filepicker\\MethodResultWrapper.kt"