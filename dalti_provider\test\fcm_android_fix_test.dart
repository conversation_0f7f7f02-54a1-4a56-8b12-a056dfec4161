import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../lib/core/services/firebase_messaging_service.dart';
import '../lib/core/services/notification_service.dart';
import '../lib/core/services/notification_api_service.dart';
import '../lib/core/storage/auth_storage_service.dart';
import '../lib/core/storage/web_storage_service.dart';

// Mock classes
class MockNotificationApiService extends Mock implements NotificationApiService {}

void main() {
  group('FCM Android Authentication Fix Tests', () {
    late MockNotificationApiService mockApiService;

    setUp(() {
      mockApiService = MockNotificationApiService();
      SharedPreferences.setMockInitialValues({});
    });

    test('NotificationService should have hasApiService getter', () {
      // Initially should be false
      expect(NotificationService.hasApiService, false);

      // After initialization should be true
      NotificationService.initialize(apiService: mockApiService);
      expect(NotificationService.hasApiService, true);
    });

    test('NotificationService.saveFcmTokenDirect should call API service', () async {
      // Arrange
      const testToken = 'test_fcm_token_123';
      when(mockApiService.saveFcmToken(fcmToken: testToken))
          .thenAnswer((_) async => true);

      await NotificationService.initialize(apiService: mockApiService);

      // Act
      final result = await NotificationService.saveFcmTokenDirect(testToken);

      // Assert
      expect(result, true);
      verify(mockApiService.saveFcmToken(fcmToken: testToken)).called(1);
    });

    test('NotificationService.saveFcmTokenDirect should return false when no API service', () async {
      // Arrange - don't initialize with API service
      const testToken = 'test_fcm_token_123';

      // Act
      final result = await NotificationService.saveFcmTokenDirect(testToken);

      // Assert
      expect(result, false);
    });

    test('NotificationService.saveFcmTokenDirect should handle API service errors', () async {
      // Arrange
      const testToken = 'test_fcm_token_123';
      when(mockApiService.saveFcmToken(fcmToken: testToken))
          .thenThrow(Exception('API Error'));

      await NotificationService.initialize(apiService: mockApiService);

      // Act
      final result = await NotificationService.saveFcmTokenDirect(testToken);

      // Assert
      expect(result, false);
      verify(mockApiService.saveFcmToken(fcmToken: testToken)).called(1);
    });

    group('Multi-source token retrieval simulation', () {
      test('Should prioritize AuthStorageService for Android', () async {
        // This test simulates the Android-specific fix
        // In a real scenario, we would mock the storage services
        
        // Arrange - simulate AuthStorageService having a token
        SharedPreferences.setMockInitialValues({
          'access_token': 'auth_storage_token_123',
        });

        // Act & Assert
        // The actual implementation would try AuthStorageService first
        // This test verifies the concept of multi-source retrieval
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('access_token');
        
        expect(token, 'auth_storage_token_123');
      });

      test('Should fallback to WebStorageService if AuthStorageService fails', () async {
        // Arrange - simulate WebStorageService having a token when AuthStorageService fails
        SharedPreferences.setMockInitialValues({
          'auth_jwt_token': '{"access_token": "web_storage_token_456"}',
        });

        // Act & Assert
        final prefs = await SharedPreferences.getInstance();
        final tokenJson = prefs.getString('auth_jwt_token');
        
        expect(tokenJson, contains('web_storage_token_456'));
      });

      test('Should fallback to SharedPreferences directly as last resort', () async {
        // Arrange - simulate direct SharedPreferences access
        SharedPreferences.setMockInitialValues({
          'access_token': 'direct_prefs_token_789',
        });

        // Act & Assert
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('access_token');
        
        expect(token, 'direct_prefs_token_789');
      });
    });
  });

  group('Integration Test Scenarios', () {
    test('Should handle Android Hive initialization failure gracefully', () {
      // This test simulates the scenario where Hive fails to initialize on Android
      // The fix should fallback to SharedPreferences
      
      // In the actual implementation, this would be handled by:
      // 1. Trying AuthStorageService (SharedPreferences-based)
      // 2. Falling back to WebStorageService 
      // 3. Finally trying SharedPreferences directly
      
      expect(true, true); // Placeholder - actual implementation would test the fallback chain
    });

    test('Should maintain iOS compatibility', () {
      // This test ensures that the fix doesn't break iOS functionality
      // iOS should continue to work with the existing WebStorageService approach
      
      expect(true, true); // Placeholder - actual implementation would test iOS compatibility
    });
  });
}
