import 'package:flutter/foundation.dart';
import '../services/firebase_messaging_service.dart';
import '../services/notification_service.dart';
import '../storage/auth_storage_service.dart';
import '../storage/web_storage_service.dart';

/// Test FCM token generation and server registration
class FCMTokenTest {
  
  /// Test FCM token functionality end-to-end
  static Future<void> testFCMTokenFlow() async {
    if (kIsWeb) {
      print('[FCMTokenTest] ⚠️ Skipping FCM token tests on web platform');
      return;
    }

    print('\n🔍 === FCM TOKEN FLOW TEST ===\n');

    await _testTokenGeneration();
    await _testTokenStorage();
    await _testAuthenticationTokens();
    await _testServerRegistration();
    
    print('\n✅ === FCM TOKEN FLOW TEST COMPLETED ===\n');
  }

  /// Test 1: FCM token generation
  static Future<void> _testTokenGeneration() async {
    print('📋 Test 1: FCM Token Generation');
    print('─' * 40);

    try {
      // Test direct Firebase token
      print('🔄 Getting token from Firebase...');
      final firebaseToken = await FirebaseMessagingService.getToken();
      
      if (firebaseToken != null) {
        print('✅ Firebase token generated');
        print('Token preview: ${firebaseToken.substring(0, 20)}...');
        print('Token length: ${firebaseToken.length}');
      } else {
        print('❌ Failed to generate Firebase token');
      }

      // Test service token
      print('\n🔄 Getting token from NotificationService...');
      final serviceToken = await NotificationService.getDeviceToken();
      
      if (serviceToken != null) {
        print('✅ Service token retrieved');
        print('Tokens match: ${firebaseToken == serviceToken}');
      } else {
        print('❌ Failed to get service token');
      }

    } catch (e) {
      print('❌ Token generation test failed: $e');
    }
    print('');
  }

  /// Test 2: Token storage mechanisms
  static Future<void> _testTokenStorage() async {
    print('📋 Test 2: Token Storage');
    print('─' * 40);

    try {
      // Test stored FCM token
      print('🔄 Checking stored FCM token...');
      final storedToken = await FirebaseMessagingService.getToken();
      
      if (storedToken != null) {
        print('✅ FCM token stored locally');
        print('Stored token preview: ${storedToken.substring(0, 20)}...');
      } else {
        print('❌ No FCM token stored locally');
      }

    } catch (e) {
      print('❌ Token storage test failed: $e');
    }
    print('');
  }

  /// Test 3: Authentication tokens for server registration
  static Future<void> _testAuthenticationTokens() async {
    print('📋 Test 3: Authentication Tokens');
    print('─' * 40);

    try {
      // Test AuthStorageService (primary method for Android)
      print('🔄 Checking AuthStorageService...');
      final authToken = await AuthStorageService.getAccessToken();
      if (authToken != null) {
        print('✅ Auth token found in AuthStorageService');
        print('Token preview: ${authToken.substring(0, 10)}...');
      } else {
        print('⚠️ No auth token in AuthStorageService');
      }

      // Test WebStorageService (fallback method)
      print('\n🔄 Checking WebStorageService...');
      final webTokenData = WebStorageService.getAuth<Map<String, dynamic>>('jwt_token');
      if (webTokenData != null) {
        final webToken = webTokenData['access_token'] as String?;
        if (webToken != null) {
          print('✅ Auth token found in WebStorageService');
          print('Token preview: ${webToken.substring(0, 10)}...');
        } else {
          print('⚠️ JWT data found but no access_token');
        }
      } else {
        print('⚠️ No auth token in WebStorageService');
      }

      // Check if user is authenticated
      final hasAuth = authToken != null || webTokenData?['access_token'] != null;
      if (hasAuth) {
        print('\n✅ User is authenticated - FCM token can be sent to server');
      } else {
        print('\n❌ User is NOT authenticated - FCM token cannot be sent to server');
        print('💡 Please log in first to test server registration');
      }

    } catch (e) {
      print('❌ Authentication token test failed: $e');
    }
    print('');
  }

  /// Test 4: Server registration
  static Future<void> _testServerRegistration() async {
    print('📋 Test 4: Server Registration');
    print('─' * 40);

    try {
      // Check if we can send token to server
      print('🔄 Testing server registration capability...');
      
      // Check if NotificationService has API service
      if (NotificationService.hasApiService) {
        print('✅ NotificationService has API service');
        
        // Get FCM token
        final token = await FirebaseMessagingService.getToken();
        if (token != null) {
          print('✅ FCM token available for registration');
          
          // Test the registration (this will use our fixed authentication)
          print('\n🔄 Attempting server registration...');
          final success = await NotificationService.saveFcmTokenDirect(token);
          
          if (success) {
            print('✅ FCM token registered with server successfully!');
          } else {
            print('❌ FCM token registration failed');
            print('💡 Check authentication status and network connectivity');
          }
        } else {
          print('❌ No FCM token available for registration');
        }
      } else {
        print('⚠️ NotificationService API service not available');
        print('💡 Make sure app is properly initialized');
      }

    } catch (e) {
      print('❌ Server registration test failed: $e');
    }
    print('');
  }

  /// Test the complete flow after login
  static Future<void> testPostLoginFlow() async {
    print('\n🔍 === POST-LOGIN FCM FLOW TEST ===\n');

    try {
      print('📋 Testing FCM token registration after login...');
      
      // This simulates what happens after successful login
      await FirebaseMessagingService.sendTokenToServerAfterLogin();
      
      print('✅ Post-login FCM flow completed');
      
    } catch (e) {
      print('❌ Post-login FCM flow failed: $e');
    }
    
    print('\n✅ === POST-LOGIN FCM FLOW TEST COMPLETED ===\n');
  }

  /// Print troubleshooting guide
  static void printTroubleshootingGuide() {
    print('\n🔧 === FCM TOKEN TROUBLESHOOTING GUIDE ===');
    
    print('\n1. Token Generation Issues:');
    print('   ❌ No token generated → Check Firebase configuration');
    print('   ❌ Token is null → Check device connectivity');
    print('   ❌ Token changes frequently → Normal behavior');
    
    print('\n2. Authentication Issues:');
    print('   ❌ No auth token → User needs to log in first');
    print('   ❌ 401 error → Authentication token expired or invalid');
    print('   ❌ Token not found → Storage initialization issue');
    
    print('\n3. Server Registration Issues:');
    print('   ❌ Registration fails → Check network connectivity');
    print('   ❌ API service unavailable → Check app initialization');
    print('   ❌ 500 error → Server-side issue');
    
    print('\n4. Platform-Specific Issues:');
    print('   📱 Android: Check notification permissions');
    print('   📱 Android: Verify channel configuration');
    print('   🍎 iOS: Check APNs certificate');
    print('   🌐 Web: Requires VAPID key');
    
    print('\n5. Testing Steps:');
    print('   1. Run FCM token flow test');
    print('   2. Log in to the app');
    print('   3. Run post-login flow test');
    print('   4. Send test notification from Firebase Console');
    print('   5. Verify notification appears on device');
    
    print('═' * 50);
  }
}
